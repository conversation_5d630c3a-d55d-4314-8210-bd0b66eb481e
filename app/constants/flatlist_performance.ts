// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

/**
 * Performance constants for FlatList optimization
 * These values are optimized for React Native FlatList performance
 */

// Rendering batch sizes
export const INITIAL_BATCH_TO_RENDER = {
    SMALL: 10,      // For simple lists with lightweight items
    MEDIUM: 15,     // For lists with moderate complexity items
    LARGE: 20,      // For complex lists or when initial load is critical
    POST_LIST: 15,  // Optimized for post lists (was 10 + 5)
} as const;

export const MAX_TO_RENDER_PER_BATCH = {
    SMALL: 5,       // Conservative for complex items
    MEDIUM: 10,     // Balanced performance
    LARGE: 15,      // For simple items or powerful devices
    POST_LIST: 10,  // Current post list value
} as const;

// Window size optimization
export const WINDOW_SIZE = {
    SMALL: 5,       // Minimal memory usage
    MEDIUM: 10,     // Balanced (React Native default)
    LARGE: 21,      // Better for fast scrolling
    POST_LIST: 15,  // Optimized for chat-like interfaces
} as const;

// Update batching periods (milliseconds)
export const UPDATE_CELLS_BATCHING_PERIOD = {
    FAST: 16,       // 60fps updates
    MEDIUM: 50,     // Balanced performance
    SLOW: 100,      // Conservative for complex items
} as const;

// Scroll event throttling (milliseconds)
export const SCROLL_EVENT_THROTTLE = {
    FAST: 16,       // Smooth scrolling
    MEDIUM: 60,     // Balanced
    SLOW: 100,      // Conservative
} as const;

// Viewability configurations
export const VIEWABILITY_CONFIG = {
    DEFAULT: {
        itemVisiblePercentThreshold: 50,
        minimumViewTime: 500,
    },
    STRICT: {
        itemVisiblePercentThreshold: 100,
        minimumViewTime: 1000,
    },
    LENIENT: {
        itemVisiblePercentThreshold: 25,
        minimumViewTime: 250,
    },
    POST_LIST: {
        itemVisiblePercentThreshold: 50,
        minimumViewTime: 500,
    },
} as const;

// onEndReached threshold values
export const END_REACHED_THRESHOLD = {
    EARLY: 0.5,     // Load more when 50% from end
    MEDIUM: 0.7,    // Load more when 30% from end
    LATE: 0.9,      // Load more when 10% from end (current post list)
} as const;

// Performance presets for different list types
export const PERFORMANCE_PRESETS = {
    // For simple lists with lightweight items (user lists, team lists)
    SIMPLE_LIST: {
        initialNumToRender: INITIAL_BATCH_TO_RENDER.SMALL,
        maxToRenderPerBatch: MAX_TO_RENDER_PER_BATCH.MEDIUM,
        windowSize: WINDOW_SIZE.MEDIUM,
        updateCellsBatchingPeriod: UPDATE_CELLS_BATCHING_PERIOD.MEDIUM,
        scrollEventThrottle: SCROLL_EVENT_THROTTLE.MEDIUM,
        removeClippedSubviews: true,
        viewabilityConfig: VIEWABILITY_CONFIG.DEFAULT,
    },
    
    // For complex lists with rich content (post lists, message lists)
    COMPLEX_LIST: {
        initialNumToRender: INITIAL_BATCH_TO_RENDER.POST_LIST,
        maxToRenderPerBatch: MAX_TO_RENDER_PER_BATCH.POST_LIST,
        windowSize: WINDOW_SIZE.POST_LIST,
        updateCellsBatchingPeriod: UPDATE_CELLS_BATCHING_PERIOD.MEDIUM,
        scrollEventThrottle: SCROLL_EVENT_THROTTLE.MEDIUM,
        removeClippedSubviews: true,
        viewabilityConfig: VIEWABILITY_CONFIG.POST_LIST,
    },
    
    // For high-performance lists that need smooth scrolling
    HIGH_PERFORMANCE: {
        initialNumToRender: INITIAL_BATCH_TO_RENDER.LARGE,
        maxToRenderPerBatch: MAX_TO_RENDER_PER_BATCH.LARGE,
        windowSize: WINDOW_SIZE.LARGE,
        updateCellsBatchingPeriod: UPDATE_CELLS_BATCHING_PERIOD.FAST,
        scrollEventThrottle: SCROLL_EVENT_THROTTLE.FAST,
        removeClippedSubviews: true,
        viewabilityConfig: VIEWABILITY_CONFIG.LENIENT,
    },
    
    // For memory-constrained environments
    MEMORY_OPTIMIZED: {
        initialNumToRender: INITIAL_BATCH_TO_RENDER.SMALL,
        maxToRenderPerBatch: MAX_TO_RENDER_PER_BATCH.SMALL,
        windowSize: WINDOW_SIZE.SMALL,
        updateCellsBatchingPeriod: UPDATE_CELLS_BATCHING_PERIOD.SLOW,
        scrollEventThrottle: SCROLL_EVENT_THROTTLE.SLOW,
        removeClippedSubviews: true,
        viewabilityConfig: VIEWABILITY_CONFIG.STRICT,
    },
} as const;

// Common keyExtractor functions
export const KEY_EXTRACTORS = {
    ID: (item: {id: string}) => item.id,
    INDEX: (_item: any, index: number) => index.toString(),
    ID_WITH_FALLBACK: (item: {id?: string}, index: number) => item.id || index.toString(),
} as const;

// Fixed item heights for getItemLayout (when items have consistent heights)
export const ITEM_HEIGHTS = {
    USER_ITEM: 60,
    TEAM_ITEM: 48,
    SIMPLE_LIST_ITEM: 44,
    CHANNEL_ITEM: 56,
} as const;
